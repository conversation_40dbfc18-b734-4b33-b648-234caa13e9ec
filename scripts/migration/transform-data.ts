#!/usr/bin/env tsx

import fs from 'fs/promises';
import path from 'path';

// Transformation configuration
interface TransformConfig {
	inputDir: string;
	outputDir: string;
	validateOutput: boolean;
	preserveIds: boolean;
}

// Transformation statistics
interface TransformStats {
	inputRecords: number;
	outputRecords: number;
	errors: string[];
	startTime: Date;
	endTime?: Date;
	duration?: number;
}

// MongoDB document interfaces
interface MongoUser {
	_id?: string;
	provider: string;
	provider_id: string;
	username?: string;
	password_hash?: string;
	disabled: boolean;
	created_at: string;
	updated_at: string;
}

interface MongoExplain {
	_id?: string;
	EN: string;
	VI: string;
}

interface MongoExample {
	_id?: string;
	EN: string;
	VI: string;
}

interface MongoDefinition {
	_id?: string;
	pos: string[];
	ipa: string;
	images: string[];
	explains: MongoExplain[];
	examples: MongoExample[];
}

interface MongoWord {
	_id?: string;
	term: string;
	language: string;
	audio_url?: string;
	definitions: MongoDefinition[];
	created_at: string;
	updated_at: string;
}

interface MongoCollection {
	_id?: string;
	name: string;
	target_language: string;
	source_language: string;
	user_id: string;
	word_ids: string[];
	paragraph_ids: string[];
	keyword_ids: string[];
	enable_learn_word_notification: boolean;
	created_at: string;
	updated_at: string;
}

interface MongoMultipleChoiceExercise {
	_id?: string;
	question: string;
	options: string[];
	answer: number;
	explanation?: string;
}

interface MongoParagraph {
	_id?: string;
	title: string;
	content: string;
	language: string;
	difficulty: string;
	length: string;
	multiple_choice_exercises: MongoMultipleChoiceExercise[];
	created_at: string;
	updated_at: string;
}

interface MongoKeyword {
	_id?: string;
	content: string;
	user_id: string;
}

interface MongoLastSeenWord {
	_id?: string;
	user_id: string;
	word_id: string;
	last_seen_at: string;
	review_count: number;
	created_at: string;
	updated_at: string;
}

interface MongoFeedback {
	_id?: string;
	message: string;
	user_id: string;
	status: string;
	created_at: string;
}

interface MongoCollectionStats {
	_id?: string;
	collection_id: string;
	user_id: string;
	date: string;
	words_reviewed_count: number;
	qa_practice_submissions: number;
	paragraph_practice_submissions: number;
	created_at: string;
	updated_at: string;
}

class DataTransformer {
	private config: TransformConfig;
	private stats: Map<string, TransformStats> = new Map();

	constructor(config: TransformConfig) {
		this.config = config;
	}

	async initialize(): Promise<void> {
		try {
			// Ensure output directory exists
			await fs.mkdir(this.config.outputDir, { recursive: true });
			console.log(`📁 Output directory: ${this.config.outputDir}`);
		} catch (error) {
			console.error('❌ Failed to initialize transformer:', error);
			throw error;
		}
	}

	async transformAllData(): Promise<void> {
		console.log('🔄 Starting data transformation...');
		
		try {
			// Transform in dependency order
			await this.transformUsers();
			await this.transformWords();
			await this.transformCollections();
			await this.transformParagraphs();
			await this.transformKeywords();
			await this.transformLastSeenWords();
			await this.transformFeedbacks();
			await this.transformCollectionStats();

			console.log('\n📊 Transformation Summary:');
			this.printSummary();
		} catch (error) {
			console.error('❌ Transformation failed:', error);
			throw error;
		}
	}

	private async transformUsers(): Promise<void> {
		const collectionName = 'users';
		console.log(`\n🔄 Transforming ${collectionName}...`);
		
		const stats: TransformStats = {
			inputRecords: 0,
			outputRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(collectionName, stats);

		try {
			const inputData = await this.readInputFile('users.json');
			stats.inputRecords = inputData.length;
			console.log(`   Input records: ${stats.inputRecords}`);

			const transformedData: MongoUser[] = inputData.map((user: any) => ({
				_id: this.config.preserveIds ? user.id : undefined,
				provider: user.provider,
				provider_id: user.provider_id,
				username: user.username,
				password_hash: user.password_hash,
				disabled: user.disabled,
				created_at: user.created_at,
				updated_at: user.updated_at,
			}));

			await this.writeOutputFile(collectionName, transformedData);
			stats.outputRecords = transformedData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();
			
			console.log(`   ✅ Transformed ${stats.outputRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error transforming ${collectionName}:`, error);
			throw error;
		}
	}

	private async transformWords(): Promise<void> {
		const collectionName = 'words';
		console.log(`\n🔄 Transforming ${collectionName}...`);
		
		const stats: TransformStats = {
			inputRecords: 0,
			outputRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(collectionName, stats);

		try {
			// Load related data
			const words = await this.readInputFile('words.json');
			const definitions = await this.readInputFile('definitions.json');
			const explains = await this.readInputFile('explains.json');
			const examples = await this.readInputFile('examples.json');

			stats.inputRecords = words.length;
			console.log(`   Input records: ${stats.inputRecords}`);

			// Group related data by IDs
			const definitionsByWordId = this.groupBy(definitions, 'word_id');
			const explainsByDefinitionId = this.groupBy(explains, 'definition_id');
			const examplesByDefinitionId = this.groupBy(examples, 'definition_id');

			const transformedData: MongoWord[] = words.map((word: any) => {
				const wordDefinitions = definitionsByWordId[word.id] || [];
				
				const mongoDefinitions: MongoDefinition[] = wordDefinitions.map((def: any) => {
					const defExplains = explainsByDefinitionId[def.id] || [];
					const defExamples = examplesByDefinitionId[def.id] || [];

					return {
						_id: this.config.preserveIds ? def.id : undefined,
						pos: def.pos,
						ipa: def.ipa,
						images: def.images,
						explains: defExplains.map((explain: any) => ({
							_id: this.config.preserveIds ? explain.id : undefined,
							EN: explain.EN,
							VI: explain.VI,
						})),
						examples: defExamples.map((example: any) => ({
							_id: this.config.preserveIds ? example.id : undefined,
							EN: example.EN,
							VI: example.VI,
						})),
					};
				});

				return {
					_id: this.config.preserveIds ? word.id : undefined,
					term: word.term,
					language: word.language,
					audio_url: word.audio_url,
					definitions: mongoDefinitions,
					created_at: word.created_at,
					updated_at: word.updated_at,
				};
			});

			await this.writeOutputFile(collectionName, transformedData);
			stats.outputRecords = transformedData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();
			
			console.log(`   ✅ Transformed ${stats.outputRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error transforming ${collectionName}:`, error);
			throw error;
		}
	}

	private async transformCollections(): Promise<void> {
		const collectionName = 'collections';
		console.log(`\n🔄 Transforming ${collectionName}...`);
		
		const stats: TransformStats = {
			inputRecords: 0,
			outputRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(collectionName, stats);

		try {
			const inputData = await this.readInputFile('collections.json');
			stats.inputRecords = inputData.length;
			console.log(`   Input records: ${stats.inputRecords}`);

			const transformedData: MongoCollection[] = inputData.map((collection: any) => ({
				_id: this.config.preserveIds ? collection.id : undefined,
				name: collection.name,
				target_language: collection.target_language,
				source_language: collection.source_language,
				user_id: collection.user_id,
				word_ids: collection.word_ids,
				paragraph_ids: collection.paragraph_ids,
				keyword_ids: collection.keyword_ids,
				enable_learn_word_notification: collection.enable_learn_word_notification,
				created_at: collection.created_at,
				updated_at: collection.updated_at,
			}));

			await this.writeOutputFile(collectionName, transformedData);
			stats.outputRecords = transformedData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();
			
			console.log(`   ✅ Transformed ${stats.outputRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error transforming ${collectionName}:`, error);
			throw error;
		}
	}

	// Helper methods
	private async readInputFile(filename: string): Promise<any[]> {
		const filePath = path.join(this.config.inputDir, filename);
		const content = await fs.readFile(filePath, 'utf8');
		return JSON.parse(content);
	}

	private async writeOutputFile(collectionName: string, data: any[]): Promise<void> {
		const filePath = path.join(this.config.outputDir, `${collectionName}.json`);
		await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
	}

	private groupBy(array: any[], key: string): Record<string, any[]> {
		return array.reduce((groups, item) => {
			const groupKey = item[key];
			if (!groups[groupKey]) {
				groups[groupKey] = [];
			}
			groups[groupKey].push(item);
			return groups;
		}, {});
	}

	private printSummary(): void {
		let totalInput = 0;
		let totalOutput = 0;
		let totalErrors = 0;

		for (const [collectionName, stats] of this.stats.entries()) {
			totalInput += stats.inputRecords;
			totalOutput += stats.outputRecords;
			totalErrors += stats.errors.length;
			
			console.log(`   ${collectionName}: ${stats.outputRecords}/${stats.inputRecords} (${stats.duration}ms)`);
		}

		console.log(`\n📈 Total: ${totalOutput}/${totalInput} records transformed`);
		if (totalErrors > 0) {
			console.log(`⚠️  Total errors: ${totalErrors}`);
		}
	}
}

// Main execution
async function main() {
	const config: TransformConfig = {
		inputDir: path.join(process.cwd(), 'data', 'postgresql-export'),
		outputDir: path.join(process.cwd(), 'data', 'mongodb-import'),
		validateOutput: true,
		preserveIds: true,
	};

	const transformer = new DataTransformer(config);

	try {
		await transformer.initialize();
		await transformer.transformAllData();
		console.log('\n🎉 Transformation completed successfully!');
	} catch (error) {
		console.error('\n💥 Transformation failed:', error);
		process.exit(1);
	}
}

// Run if called directly
if (require.main === module) {
	main().catch(console.error);
}

export { DataTransformer, TransformConfig, TransformStats };
